package repositories

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"cloudweave/internal/models"

	"github.com/lib/pq"
)

// VulnerabilityRepository handles vulnerability data operations
type VulnerabilityRepository struct {
	db *sql.DB
}

// NewVulnerabilityRepository creates a new vulnerability repository
func NewVulnerabilityRepository(db *sql.DB) *VulnerabilityRepository {
	return &VulnerabilityRepository{db: db}
}

// Create creates a new vulnerability
func (r *VulnerabilityRepository) Create(ctx context.Context, vulnerability *models.Vulnerability) error {
	query := `
		INSERT INTO vulnerabilities (
			id, organization_id, scan_id, title, description, severity, status, cve_id, cvss_score,
			resource_type, resource_id, resource_name, recommendation, reference_links, tags,
			first_detected, last_seen, resolved_at, created_at, updated_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20)
	`

	_, err := r.db.ExecContext(ctx, query,
		vulnerability.ID, vulnerability.OrganizationID, vulnerability.ScanID, vulnerability.Title,
		vulnerability.Description, vulnerability.Severity, vulnerability.Status, vulnerability.CVEID,
		vulnerability.CVSSScore, vulnerability.ResourceType, vulnerability.ResourceID,
		vulnerability.ResourceName, vulnerability.Recommendation, pq.Array(vulnerability.References),
		pq.Array(vulnerability.Tags), vulnerability.FirstDetected, vulnerability.LastSeen,
		vulnerability.ResolvedAt, vulnerability.CreatedAt, vulnerability.UpdatedAt,
	)

	if err != nil {
		return fmt.Errorf("failed to create vulnerability: %w", err)
	}

	return nil
}

// GetByID retrieves a vulnerability by ID
func (r *VulnerabilityRepository) GetByID(ctx context.Context, orgID, id string) (*models.Vulnerability, error) {
	query := `
		SELECT id, organization_id, scan_id, title, description, severity, status, cve_id, cvss_score,
			   resource_type, resource_id, resource_name, recommendation, reference_links, tags,
			   first_detected, last_seen, resolved_at, created_at, updated_at
		FROM vulnerabilities
		WHERE id = $1 AND organization_id = $2
	`

	row := r.db.QueryRowContext(ctx, query, id, orgID)

	vulnerability := &models.Vulnerability{}
	err := row.Scan(
		&vulnerability.ID, &vulnerability.OrganizationID, &vulnerability.ScanID, &vulnerability.Title,
		&vulnerability.Description, &vulnerability.Severity, &vulnerability.Status, &vulnerability.CVEID,
		&vulnerability.CVSSScore, &vulnerability.ResourceType, &vulnerability.ResourceID,
		&vulnerability.ResourceName, &vulnerability.Recommendation, pq.Array(&vulnerability.References),
		pq.Array(&vulnerability.Tags), &vulnerability.FirstDetected, &vulnerability.LastSeen,
		&vulnerability.ResolvedAt, &vulnerability.CreatedAt, &vulnerability.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("vulnerability not found")
		}
		return nil, fmt.Errorf("failed to get vulnerability: %w", err)
	}

	return vulnerability, nil
}

// Update updates a vulnerability
func (r *VulnerabilityRepository) Update(ctx context.Context, vulnerability *models.Vulnerability) error {
	query := `
		UPDATE vulnerabilities SET
			title = $2, description = $3, severity = $4, status = $5, cve_id = $6, cvss_score = $7,
			resource_type = $8, resource_id = $9, resource_name = $10, recommendation = $11,
			reference_links = $12, tags = $13, last_seen = $14, resolved_at = $15, updated_at = $16
		WHERE id = $1
	`

	_, err := r.db.ExecContext(ctx, query,
		vulnerability.ID, vulnerability.Title, vulnerability.Description, vulnerability.Severity,
		vulnerability.Status, vulnerability.CVEID, vulnerability.CVSSScore, vulnerability.ResourceType,
		vulnerability.ResourceID, vulnerability.ResourceName, vulnerability.Recommendation,
		pq.Array(vulnerability.References), pq.Array(vulnerability.Tags), vulnerability.LastSeen,
		vulnerability.ResolvedAt, vulnerability.UpdatedAt,
	)

	if err != nil {
		return fmt.Errorf("failed to update vulnerability: %w", err)
	}

	return nil
}

// Delete deletes a vulnerability
func (r *VulnerabilityRepository) Delete(ctx context.Context, id string) error {
	query := `DELETE FROM vulnerabilities WHERE id = $1`

	result, err := r.db.ExecContext(ctx, query, id)
	if err != nil {
		return fmt.Errorf("failed to delete vulnerability: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("vulnerability not found")
	}

	return nil
}

// Query retrieves vulnerabilities based on query parameters
func (r *VulnerabilityRepository) Query(ctx context.Context, orgID string, query models.VulnerabilityQuery) ([]*models.Vulnerability, int, error) {
	// Build WHERE clause
	whereConditions := []string{"organization_id = $1"}
	args := []interface{}{orgID}
	argIndex := 2

	if query.ScanID != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("scan_id = $%d", argIndex))
		args = append(args, *query.ScanID)
		argIndex++
	}

	if query.Severity != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("severity = $%d", argIndex))
		args = append(args, *query.Severity)
		argIndex++
	}

	if query.Status != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("status = $%d", argIndex))
		args = append(args, *query.Status)
		argIndex++
	}

	if query.ResourceType != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("resource_type = $%d", argIndex))
		args = append(args, *query.ResourceType)
		argIndex++
	}

	if query.ResourceID != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("resource_id = $%d", argIndex))
		args = append(args, *query.ResourceID)
		argIndex++
	}

	if query.CVEID != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("cve_id = $%d", argIndex))
		args = append(args, *query.CVEID)
		argIndex++
	}

	if query.StartDate != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("created_at >= $%d", argIndex))
		args = append(args, *query.StartDate)
		argIndex++
	}

	if query.EndDate != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("created_at <= $%d", argIndex))
		args = append(args, *query.EndDate)
		argIndex++
	}

	whereClause := strings.Join(whereConditions, " AND ")

	// Get total count
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM vulnerabilities WHERE %s", whereClause)
	var total int
	err := r.db.QueryRowContext(ctx, countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get total count: %w", err)
	}

	// Set default pagination
	limit := query.Limit
	if limit <= 0 {
		limit = 50
	}
	offset := query.Offset
	if offset < 0 {
		offset = 0
	}

	// Get vulnerabilities
	selectQuery := fmt.Sprintf(`
		SELECT id, organization_id, scan_id, title, description, severity, status, cve_id, cvss_score,
			   resource_type, resource_id, resource_name, recommendation, reference_links, tags,
			   first_detected, last_seen, resolved_at, created_at, updated_at
		FROM vulnerabilities
		WHERE %s
		ORDER BY created_at DESC
		LIMIT $%d OFFSET $%d
	`, whereClause, argIndex, argIndex+1)

	args = append(args, limit, offset)

	rows, err := r.db.QueryContext(ctx, selectQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to query vulnerabilities: %w", err)
	}
	defer rows.Close()

	var vulnerabilities []*models.Vulnerability
	for rows.Next() {
		vulnerability := &models.Vulnerability{}
		err := rows.Scan(
			&vulnerability.ID, &vulnerability.OrganizationID, &vulnerability.ScanID, &vulnerability.Title,
			&vulnerability.Description, &vulnerability.Severity, &vulnerability.Status, &vulnerability.CVEID,
			&vulnerability.CVSSScore, &vulnerability.ResourceType, &vulnerability.ResourceID,
			&vulnerability.ResourceName, &vulnerability.Recommendation, pq.Array(&vulnerability.References),
			pq.Array(&vulnerability.Tags), &vulnerability.FirstDetected, &vulnerability.LastSeen,
			&vulnerability.ResolvedAt, &vulnerability.CreatedAt, &vulnerability.UpdatedAt,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to scan row: %w", err)
		}

		vulnerabilities = append(vulnerabilities, vulnerability)
	}

	if err := rows.Err(); err != nil {
		return nil, 0, fmt.Errorf("failed to iterate rows: %w", err)
	}

	return vulnerabilities, total, nil
}

// GetCountsBySeverity gets vulnerability counts grouped by severity
func (r *VulnerabilityRepository) GetCountsBySeverity(ctx context.Context, orgID string) (map[models.VulnerabilitySeverity]int, error) {
	query := `
		SELECT severity, COUNT(*)
		FROM vulnerabilities
		WHERE organization_id = $1
		GROUP BY severity
	`

	rows, err := r.db.QueryContext(ctx, query, orgID)
	if err != nil {
		return nil, fmt.Errorf("failed to get severity counts: %w", err)
	}
	defer rows.Close()

	counts := make(map[models.VulnerabilitySeverity]int)
	for rows.Next() {
		var severity models.VulnerabilitySeverity
		var count int
		err := rows.Scan(&severity, &count)
		if err != nil {
			return nil, fmt.Errorf("failed to scan row: %w", err)
		}
		counts[severity] = count
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("failed to iterate rows: %w", err)
	}

	return counts, nil
}

// GetCountsByStatus gets vulnerability counts grouped by status
func (r *VulnerabilityRepository) GetCountsByStatus(ctx context.Context, orgID string) (map[models.VulnerabilityStatus]int, error) {
	query := `
		SELECT status, COUNT(*)
		FROM vulnerabilities
		WHERE organization_id = $1
		GROUP BY status
	`

	rows, err := r.db.QueryContext(ctx, query, orgID)
	if err != nil {
		return nil, fmt.Errorf("failed to get status counts: %w", err)
	}
	defer rows.Close()

	counts := make(map[models.VulnerabilityStatus]int)
	for rows.Next() {
		var status models.VulnerabilityStatus
		var count int
		err := rows.Scan(&status, &count)
		if err != nil {
			return nil, fmt.Errorf("failed to scan row: %w", err)
		}
		counts[status] = count
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("failed to iterate rows: %w", err)
	}

	return counts, nil
}
