{"swagger": "2.0", "info": {"contact": {}}, "paths": {"/auth/login": {"post": {"description": "Authenticate user with email and password", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "User login", "parameters": [{"description": "Login credentials", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.LoginRequest"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/handlers.SuccessResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.LoginResponse"}}}]}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/handlers.ValidationErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handlers.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/handlers.ErrorResponse"}}}}}, "/auth/register": {"post": {"description": "Register a new user account", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "User registration", "parameters": [{"description": "Registration details", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.RegisterRequest"}}], "responses": {"201": {"description": "Created", "schema": {"allOf": [{"$ref": "#/definitions/handlers.SuccessResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.RegisterResponse"}}}]}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/handlers.ValidationErrorResponse"}}, "409": {"description": "Conflict", "schema": {"$ref": "#/definitions/handlers.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/handlers.ErrorResponse"}}}}}, "/info": {"get": {"description": "Returns basic information about the CloudWeave API", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["System"], "summary": "Get API information", "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/models.ApiResponse"}, {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": true}}}]}}}}}, "/infrastructure": {"post": {"security": [{"BearerAuth": []}], "description": "Create a new infrastructure resource in the specified cloud provider", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Infrastructure"], "summary": "Create infrastructure resource", "parameters": [{"description": "Infrastructure creation details", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.CreateInfrastructureRequest"}}], "responses": {"201": {"description": "Created", "schema": {"allOf": [{"$ref": "#/definitions/handlers.SuccessResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.Infrastructure"}}}]}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/handlers.ValidationErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handlers.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/handlers.ErrorResponse"}}}}}}, "definitions": {"handlers.ErrorResponse": {"type": "object", "properties": {"error": {"$ref": "#/definitions/models.ApiError"}, "requestId": {"type": "string", "example": "req_123e4567-e89b-12d3-a456-426614174000"}, "success": {"type": "boolean", "example": false}}}, "handlers.SuccessResponse": {"type": "object", "properties": {"data": {}, "requestId": {"type": "string", "example": "req_123e4567-e89b-12d3-a456-426614174000"}, "success": {"type": "boolean", "example": true}}}, "handlers.ValidationErrorDetails": {"type": "object", "properties": {"code": {"type": "string", "example": "VALIDATION_ERROR"}, "details": {"type": "array", "items": {"$ref": "#/definitions/models.ValidationError"}}, "message": {"type": "string", "example": "Request validation failed"}, "timestamp": {"type": "string", "example": "2024-01-01T12:00:00Z"}}}, "handlers.ValidationErrorResponse": {"type": "object", "properties": {"error": {"$ref": "#/definitions/handlers.ValidationErrorDetails"}, "requestId": {"type": "string", "example": "req_123e4567-e89b-12d3-a456-426614174000"}, "success": {"type": "boolean", "example": false}}}, "models.ApiError": {"type": "object", "properties": {"code": {"type": "string"}, "details": {}, "message": {"type": "string"}, "timestamp": {"type": "string"}}}, "models.ApiResponse": {"type": "object", "properties": {"data": {}, "error": {"$ref": "#/definitions/models.ApiError"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "models.CreateInfrastructureRequest": {"type": "object", "required": ["name", "provider", "region", "type"], "properties": {"name": {"type": "string", "maxLength": 255, "minLength": 1, "example": "web-server-01"}, "provider": {"type": "string", "example": "aws"}, "region": {"type": "string", "maxLength": 100, "minLength": 1, "example": "us-east-1"}, "specifications": {"type": "object", "additionalProperties": true}, "tags": {"type": "array", "items": {"type": "string"}, "example": ["[\"production\"", "\"web\"]"]}, "type": {"type": "string", "example": "server"}}}, "models.Infrastructure": {"type": "object", "properties": {"costInfo": {"type": "object", "additionalProperties": true}, "createdAt": {"type": "string"}, "externalId": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "organizationId": {"type": "string"}, "provider": {"type": "string"}, "region": {"type": "string"}, "specifications": {"type": "object", "additionalProperties": true}, "status": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}, "type": {"type": "string"}, "updatedAt": {"type": "string"}}}, "models.LoginRequest": {"type": "object", "required": ["email", "password"], "properties": {"email": {"type": "string"}, "password": {"type": "string", "minLength": 8}}}, "models.LoginResponse": {"type": "object", "properties": {"refreshToken": {"type": "string"}, "success": {"type": "boolean"}, "token": {"type": "string"}, "user": {"$ref": "#/definitions/models.User"}}}, "models.RegisterRequest": {"type": "object", "required": ["confirmPassword", "email", "name", "password"], "properties": {"companyName": {"description": "Used to create organization if not provided", "type": "string"}, "confirmPassword": {"type": "string", "minLength": 8}, "email": {"type": "string"}, "name": {"type": "string", "minLength": 2}, "organizationId": {"description": "Optional - will create if not provided", "type": "string"}, "password": {"type": "string", "minLength": 8}}}, "models.RegisterResponse": {"type": "object", "properties": {"refreshToken": {"type": "string"}, "success": {"type": "boolean"}, "token": {"type": "string"}, "user": {"$ref": "#/definitions/models.User"}}}, "models.User": {"type": "object", "properties": {"avatarUrl": {"type": "string"}, "createdAt": {"type": "string"}, "email": {"type": "string"}, "emailVerified": {"type": "boolean"}, "id": {"type": "string"}, "isActive": {"type": "boolean"}, "lastLoginAt": {"type": "string"}, "name": {"type": "string"}, "organizationId": {"type": "string"}, "preferences": {"type": "object", "additionalProperties": true}, "role": {"description": "Virtual fields for compatibility", "type": "string"}, "ssoProvider": {"description": "SSO fields", "type": "string"}, "ssoSubject": {"type": "string"}, "updatedAt": {"type": "string"}}}, "models.ValidationError": {"type": "object", "properties": {"field": {"type": "string"}, "message": {"type": "string"}, "tag": {"type": "string"}, "value": {"type": "string"}}}}}