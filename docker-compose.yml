version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: cloudweave-postgres
    environment:
      POSTGRES_DB: cloud_platform_db
      POSTGRES_USER: cloudweave
      POSTGRES_PASSWORD: cloudweave123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/scripts/seed-db.sql:/docker-entrypoint-initdb.d/seed-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U cloudweave -d cloud_platform_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend Service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: cloudweave-backend
    environment:
      NODE_ENV: production
      PORT: 3001
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: cloud_platform_db
      DB_USER: cloudweave
      DB_PASSWORD: cloudweave123
      DB_SSL_MODE: disable
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
      CORS_ORIGIN: http://localhost
    ports:
      - "3001:3001"
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3001/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: cloudweave-frontend
    ports:
      - "80:80"
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data: