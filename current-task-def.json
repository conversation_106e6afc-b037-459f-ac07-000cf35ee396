{"taskDefinition": {"taskDefinitionArn": "arn:aws:ecs:us-east-1:449663741562:task-definition/cloudweave-frontend:1", "containerDefinitions": [{"name": "frontend", "image": "449663741562.dkr.ecr.us-east-1.amazonaws.com/cloudweave/frontend:latest", "cpu": 0, "portMappings": [{"containerPort": 80, "hostPort": 80, "protocol": "tcp"}], "essential": true, "environment": [], "mountPoints": [], "volumesFrom": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/cloudweave-frontend", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}, "healthCheck": {"command": ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost/ || exit 1"], "interval": 30, "timeout": 5, "retries": 3, "startPeriod": 30}, "systemControls": []}], "family": "cloudweave-frontend", "executionRoleArn": "arn:aws:iam::449663741562:role/cloudweave-ecs-task-execution-role", "networkMode": "awsvpc", "revision": 1, "volumes": [], "status": "ACTIVE", "requiresAttributes": [{"name": "com.amazonaws.ecs.capability.logging-driver.awslogs"}, {"name": "ecs.capability.execution-role-awslogs"}, {"name": "com.amazonaws.ecs.capability.ecr-auth"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.19"}, {"name": "ecs.capability.container-health-check"}, {"name": "ecs.capability.execution-role-ecr-pull"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.18"}, {"name": "ecs.capability.task-eni"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.29"}], "placementConstraints": [], "compatibilities": ["EC2", "FARGATE"], "requiresCompatibilities": ["FARGATE"], "cpu": "256", "memory": "512", "registeredAt": "2025-08-02T18:46:23.326000-04:00", "registeredBy": "arn:aws:iam::449663741562:root"}, "tags": []}