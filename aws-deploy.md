│  cd Projects/CloudWeave                                                                                              │
│  # Build new images                                                                                                  │
│  docker build -t cloudweave-backend ./backend                                                                        │
│  docker build -t cloudweave-frontend ./frontend                                                                      │
│                                                                                                                      │
│  # Login to ECR                                                                                                      │
│  aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin                        │
│  449663741562.dkr.ecr.us-east-1.amazonaws.com                                                                        │
│                                                                                                                      │
│  # Tag and push                                                                                                      │
│  docker tag cloudweave-backend:latest 449663741562.dkr.ecr.us-east-1.amazonaws.com/cloudweave-backend:latest         │
│  docker tag cloudweave-frontend:latest 449663741562.dkr.ecr.us-east-1.amazonaws.com/cloudweave-frontend:latest       │
│  docker push 449663741562.dkr.ecr.us-east-1.amazonaws.com/cloudweave-backend:latest                                  │
│  docker push 449663741562.dkr.ecr.us-east-1.amazonaws.com/cloudweave-frontend:latest                                 │
│                                                                                                                      │
│  # Update services                                                                                                   │
│  aws ecs update-service --cluster cloudweave-cluster --service cloudweave-backend --force-new-deployment --region    │
│  us-east-1                                                                                                           │
│  aws ecs update-service --cluster cloudweave-cluster --service cloudweave-frontend --force-new-deployment --region   │
│  us-east-1