/* Modern App Styles */
#root {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

/* Remove default styles that conflict */
.logo {
  display: none;
}

.card {
  display: none;
}

.read-the-docs {
  display: none;
}

/* Ensure smooth animations */
* {
  box-sizing: border-box;
}

/* Custom focus styles for better accessibility */
button:focus-visible,
input:focus-visible,
[tabindex]:focus-visible {
  outline: 2px solid #7C3AED;
  outline-offset: 2px;
  border-radius: 8px;
}

/* Improve text selection */
::selection {
  background: rgba(124, 58, 237, 0.3);
  color: inherit;
}

/* Loading states */
.loading {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Responsive design helpers */
@media (max-width: 768px) {
  .desktop-only {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .mobile-only {
    display: none !important;
  }
}