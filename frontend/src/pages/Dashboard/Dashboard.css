/* Dashboard CSS Animations */

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.pulse-indicator {
  animation: pulse 2s infinite;
}

.real-time-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  opacity: 0.8;
}

.real-time-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #10B981;
  animation: pulse 2s infinite;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.activity-item {
  padding: 16px 0;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  transition: opacity 0.3s ease;
}

.activity-item:hover {
  opacity: 0.8;
}

.activity-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #7C3AED;
  flex-shrink: 0;
}

.activity-type {
  font-weight: 500;
  text-transform: uppercase;
  font-size: 11px;
  letter-spacing: 0.5px;
}

.activity-timestamp {
  opacity: 0.6;
  font-size: 12px;
  margin-left: 8px;
}

/* Loading states */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  text-align: center;
  padding: 40px;
}

.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #EF4444;
  text-align: center;
}

/* Dashboard header animations */
.dashboard-header {
  animation: fadeInDown 0.5s ease-out;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Card hover effects */
.dashboard-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Tab transitions */
.tab-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .real-time-indicator {
    font-size: 11px;
  }
  
  .activity-item {
    font-size: 13px;
    padding: 12px 0;
  }
}

/* Data update animations */
.stat-value {
  transition: all 0.3s ease;
}

.stat-value.updated {
  animation: highlightUpdate 0.6s ease;
}

@keyframes highlightUpdate {
  0% {
    background-color: rgba(16, 185, 129, 0.2);
    transform: scale(1);
  }
  50% {
    background-color: rgba(16, 185, 129, 0.3);
    transform: scale(1.02);
  }
  100% {
    background-color: transparent;
    transform: scale(1);
  }
}