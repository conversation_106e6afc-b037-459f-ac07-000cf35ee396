.DS_Store
.vscode/
backend/.env
backend/.env.example
backend/.gitignore
backend/backend.log
# Build artifacts
backend/main
backend/migrate
backend/cloudweave
backend/tmp_*
backend/bin/
**/bin/
tmp_rovodev_*

# Database files
*.db
database.db

# Log files
*.log

# Test files
*_test.go
*.test.ts
*.test.tsx
**/test/
**/__tests__/

# Temporary files
temp_*.go
*_temp.*

# Development documentation
IMPLEMENTATION_SUMMARY.md
DATABASE.md
tasks.md
plans/

# Terraform files
.terraform/
.terraform.lock.hcl
*.tfstate
*.tfstate.*
*.tfvars
*.tfplan

# AWS files
aws/

# Node modules and dependencies
node_modules/